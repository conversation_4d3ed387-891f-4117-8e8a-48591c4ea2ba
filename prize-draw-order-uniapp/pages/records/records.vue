<template>
    <view class="records-page" :style="{ background: backgroundGradient }">

        <!-- 筛选标签 -->
        <view class="filter-tabs">
            <view class="tab-item" :class="{ active: currentTab === 'all' }" @click="switchTab('all')">
                <text>全部记录</text>
            </view>
            <view class="tab-item" :class="{ active: currentTab === 'winning' }" @click="switchTab('winning')">
                <text>中奖记录</text>
            </view>
            <view class="tab-item" :class="{ active: currentTab === 'unclaimed' }" @click="switchTab('unclaimed')">
                <text>待领取</text>
                <view class="badge" v-if="unclaimedCount > 0">{{ unclaimedCount }}</view>
            </view>
        </view>

        <!-- 加载状态 -->
        <view class="loading-container" v-if="isLoading">
            <view class="loading-spinner"></view>
            <text class="loading-text">加载中...</text>
        </view>

        <!-- 记录列表 -->
        <view class="records-container" v-else>
            <!-- 空状态 -->
            <view class="empty-state" v-if="displayRecords.length === 0">
                <image src="/static/logo.png" class="empty-image" mode="aspectFit"></image>
                <text class="empty-title">{{ getEmptyTitle() }}</text>
                <text class="empty-subtitle">{{ getEmptySubtitle() }}</text>
                <view class="empty-action" v-if="currentTab !== 'all'">
                    <view class="btn btn-primary" @click="goToLottery">
                        <text>去抽奖</text>
                    </view>
                </view>
            </view>

            <!-- 记录列表 -->
            <view class="records-list" v-else>
                <view class="record-item" v-for="record in displayRecords" :key="record.recordId"
                    @click="handleRecordClick(record)">
                    <!-- 记录卡片 -->
                    <view class="record-card">
                        <!-- 奖品信息 -->
                        <view class="prize-info">
                            <view class="prize-icon">
                                <image :src="getPrizeIcon(record)" class="icon-img" mode="aspectFit"
                                    @error="handleIconError"></image>
                            </view>
                            <view class="prize-details">
                                <text class="prize-name">{{ record.prizeName }}</text>
                                <text class="prize-desc" v-if="record.prizeDesc">{{ record.prizeDesc }}</text>
                                <text class="draw-time">{{ formatDateTime(record.drawTime) }}</text>
                            </view>
                        </view>

                        <!-- 状态标签 -->
                        <view class="status-section">
                            <view class="status-badge" :class="getStatusClass(record)">
                                <text class="status-text">{{ getStatusText(record) }}</text>
                            </view>


                        </view>
                    </view>

                    <!-- 活动信息 -->
                    <view class="activity-info" v-if="record.activityName">
                        <text class="activity-name">活动：{{ record.activityName }}</text>
                    </view>
                </view>
            </view>
        </view>

        <!-- 下拉刷新提示 -->
        <view class="refresh-tip" v-if="isRefreshing">
            <text>正在刷新...</text>
        </view>

        <!-- 底部提示 -->
        <view class="bottom-tip" v-if="displayRecords.length > 0 && !isLoading">
            <text>已显示全部记录</text>
        </view>
    </view>
</template>

<script>
import { lotteryApi, getImageUrl } from '@/utils/api.js'
import { formatDateTime, showLoading, hideLoading, showError, getUserInfo } from '@/utils/utils.js'

export default {
    data() {
        return {
            // 用户信息
            userOpenid: '',

            // 页面状态
            isLoading: true,
            isRefreshing: false,

            // 标签页
            currentTab: 'all', // all, winning, unclaimed

            // 记录数据
            allRecords: [],
            winningRecords: [],
            unclaimedRecords: [],

            // 统计数据
            unclaimedCount: 0,

            // 错误处理
            imageErrors: new Set()
        }
    },

    computed: {
        // 当前显示的记录
        displayRecords() {
            switch (this.currentTab) {
                case 'winning':
                    return this.winningRecords
                case 'unclaimed':
                    return this.unclaimedRecords
                default:
                    return this.allRecords
            }
        },

        // 主题色彩
        primaryColor() {
            return '#667eea' // 默认主题色，可以从配置中获取
        },

        // 背景渐变色
        backgroundGradient() {
            const color = this.primaryColor
            // 生成基于主题色的渐变背景
            return `linear-gradient(135deg, ${color} 0%, ${this.adjustColor(color, -20)} 100%)`
        }
    },

    onLoad() {
        this.initPage()
    },
    onShow() {
        this.initPage()
    },

    onPullDownRefresh() {
        this.refreshData()
    },

    onReachBottom() {
        // 可以在这里实现分页加载
    },

    methods: {
        async initPage() {
            try {
                // 获取用户openid（实际项目中需要通过微信登录获取）
                this.userOpenid = this.getUserOpenid()

                if (!this.userOpenid) {
                    showError('用户信息获取失败')
                    return
                }

                await this.loadAllData()
            } catch (error) {
                console.error('页面初始化失败:', error)
                showError('页面加载失败')
            } finally {
                this.isLoading = false
            }
        },

        // 获取用户openid
        getUserOpenid() {
            // 优先从URL参数获取（测试用）
            const pages = getCurrentPages()
            const currentPage = pages[pages.length - 1]
            const options = currentPage.options || {}

            if (options.userOpenid) {
                return options.userOpenid
            }

            // 从本地存储获取
            const userInfo = getUserInfo()
            if (userInfo.openid) {
                return userInfo.openid
            }

            // 测试用固定openid
            return 'test_user_001'
        },

        // 加载所有数据
        async loadAllData() {
            await Promise.all([
                this.loadAllRecords(),
                this.loadWinningRecords(),
                this.loadUnclaimedRecords()
            ])
        },

        // 加载全部记录
        async loadAllRecords() {
            try {
                const res = await lotteryApi.getUserRecords(this.userOpenid)
                if (res.code === 200) {
                    this.allRecords = res.data || []
                }
            } catch (error) {
                console.error('获取全部记录失败:', error)
            }
        },

        // 加载中奖记录
        async loadWinningRecords() {
            try {
                const res = await lotteryApi.getUserWinningRecords(this.userOpenid)
                if (res.code === 200) {
                    this.winningRecords = res.data || []
                }
            } catch (error) {
                console.error('获取中奖记录失败:', error)
            }
        },

        // 加载未领取记录
        async loadUnclaimedRecords() {
            try {
                const res = await lotteryApi.getUserUnclaimedRecords(this.userOpenid)
                if (res.code === 200) {
                    this.unclaimedRecords = res.data || []
                    this.unclaimedCount = this.unclaimedRecords.length
                }
            } catch (error) {
                console.error('获取未领取记录失败:', error)
            }
        },

        // 刷新数据
        async refreshData() {
            this.isRefreshing = true
            try {
                await this.loadAllData()
                uni.showToast({
                    title: '刷新成功',
                    icon: 'success'
                })
            } catch (error) {
                showError('刷新失败')
            } finally {
                this.isRefreshing = false
                uni.stopPullDownRefresh()
            }
        },

        // 切换标签页
        switchTab(tab) {
            this.currentTab = tab
        },

        // 处理记录点击
        handleRecordClick(record) {
            // 如果是中奖且未领取，跳转到领取页面
            if (record.isWinner === '1' && record.claimStatus === '0') {
                this.goToClaim(record)
            } else {
                // 显示记录详情
                this.showRecordDetail(record)
            }
        },

        // 跳转到领取页面
        goToClaim(record) {
            uni.navigateTo({
                url: `/pages/claim/claim?recordId=${record.recordId}`
            })
        },

        // 跳转到抽奖页面
        goToLottery() {
            // 获取商家编码和桌台号
            const pages = getCurrentPages()
            const currentPage = pages[pages.length - 1]
            const options = currentPage.options || {}

            let url = '/pages/lottery/lottery'
            if (options.merchantCode) {
                url += `?merchantCode=${options.merchantCode}`
                if (options.tableNumber) {
                    url += `&tableNumber=${options.tableNumber}`
                }
            }

            uni.navigateTo({ url })
        },

        // 显示记录详情
        showRecordDetail(record) {
            const statusText = this.getStatusText(record)
            const timeText = formatDateTime(record.drawTime)

            let content = `奖品：${record.prizeName}\n`
            if (record.prizeDesc) {
                content += `描述：${record.prizeDesc}\n`
            }
            content += `时间：${timeText}\n状态：${statusText}`

            if (record.activityName) {
                content += `\n活动：${record.activityName}`
            }

            uni.showModal({
                title: '记录详情',
                content: content,
                showCancel: false,
                confirmText: '确定'
            })
        },

        // 获取奖品图标
        getPrizeIcon(record) {
            // 如果有自定义图标且没有加载错误，使用自定义图标
            if (record.prizeImage && !this.imageErrors.has(record.prizeImage)) {
                return getImageUrl(record.prizeImage)
            }

            // 根据中奖状态返回默认图标
            if (record.isWinner === '1') {
                return '/static/logo.png' // 使用现有的logo作为默认图标
            } else {
                return '/static/logo.png' // 使用现有的logo作为默认图标
            }
        },

        // 处理图标加载错误
        handleIconError(e) {
            const src = e.target.src || e.detail.src
            if (src) {
                this.imageErrors.add(src)
            }
        },

        // 获取状态样式类
        getStatusClass(record) {
            if (record.isWinner === '1') {
                return record.claimStatus === '1' ? 'claimed' : 'unclaimed'
            } else {
                return 'not-won'
            }
        },

        // 获取状态文本
        getStatusText(record) {
            if (record.isWinner === '1') {
                return record.claimStatus === '1' ? '已领取' : '待领取'
            } else {
                return '未中奖'
            }
        },

        // 获取空状态标题
        getEmptyTitle() {
            switch (this.currentTab) {
                case 'winning':
                    return '暂无中奖记录'
                case 'unclaimed':
                    return '暂无待领取奖品'
                default:
                    return '暂无抽奖记录'
            }
        },

        // 获取空状态副标题
        getEmptySubtitle() {
            switch (this.currentTab) {
                case 'winning':
                    return '参与更多抽奖活动，赢取丰厚奖品'
                case 'unclaimed':
                    return '您的奖品都已领取完毕'
                default:
                    return '快去参与抽奖活动吧'
            }
        },

        // 格式化日期时间
        formatDateTime(dateTime) {
            return formatDateTime(dateTime)
        },

        // 颜色调整工具方法
        adjustColor(color, amount) {
            // 将十六进制颜色转换为RGB
            const hex = color.replace('#', '')
            const r = parseInt(hex.substr(0, 2), 16)
            const g = parseInt(hex.substr(2, 2), 16)
            const b = parseInt(hex.substr(4, 2), 16)

            // 调整亮度
            const newR = Math.max(0, Math.min(255, r + amount))
            const newG = Math.max(0, Math.min(255, g + amount))
            const newB = Math.max(0, Math.min(255, b + amount))

            // 转换回十六进制
            return `#${newR.toString(16).padStart(2, '0')}${newG.toString(16).padStart(2, '0')}${newB.toString(16).padStart(2, '0')}`
        }
    }
}
</script>

<style lang="scss" scoped>
.records-page {
    min-height: 100vh;
    /* 背景色通过内联样式动态设置 */
    padding-bottom: 20rpx;
}



/* 筛选标签 */
.filter-tabs {
    display: flex;
    background: rgba(255, 255, 255, 0.95);
    margin: 60rpx 30rpx 20rpx;
    border-radius: 25rpx;
    padding: 8rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);

    .tab-item {
        flex: 1;
        text-align: center;
        padding: 20rpx 10rpx;
        border-radius: 20rpx;
        position: relative;
        transition: all 0.3s ease;

        text {
            font-size: 28rpx;
            color: #666;
            font-weight: 500;
        }

        .badge {
            position: absolute;
            top: 10rpx;
            right: 15rpx;
            background: #ff4757;
            color: white;
            font-size: 20rpx;
            padding: 4rpx 8rpx;
            border-radius: 10rpx;
            min-width: 20rpx;
            text-align: center;
        }

        &.active {
            background: linear-gradient(135deg, #667eea, #764ba2);
            box-shadow: 0 4rpx 15rpx rgba(102, 126, 234, 0.4);

            text {
                color: white;
                font-weight: bold;
            }

            .badge {
                background: #ff6b7a;
            }
        }
    }
}

/* 加载状态 */
.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 100rpx 0;

    .loading-spinner {
        width: 60rpx;
        height: 60rpx;
        border: 4rpx solid rgba(255, 255, 255, 0.3);
        border-top: 4rpx solid #ffffff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 20rpx;
    }

    .loading-text {
        color: rgba(255, 255, 255, 0.8);
        font-size: 28rpx;
    }
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* 记录容器 */
.records-container {
    padding: 0 30rpx;
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 100rpx 40rpx;

    .empty-image {
        width: 200rpx;
        height: 200rpx;
        margin-bottom: 40rpx;
        opacity: 0.6;
    }

    .empty-title {
        display: block;
        font-size: 32rpx;
        color: rgba(255, 255, 255, 0.9);
        font-weight: bold;
        margin-bottom: 15rpx;
    }

    .empty-subtitle {
        display: block;
        font-size: 26rpx;
        color: rgba(255, 255, 255, 0.7);
        margin-bottom: 40rpx;
    }

    .empty-action {
        .btn {
            display: inline-block;
            padding: 25rpx 50rpx;
            border-radius: 25rpx;
            font-size: 28rpx;
            font-weight: bold;

            &.btn-primary {
                background: linear-gradient(135deg, #ff6b7a, #ff8e9b);
                color: white;
                box-shadow: 0 8rpx 25rpx rgba(255, 107, 122, 0.4);
            }
        }
    }
}

/* 记录列表 */
.records-list {
    .record-item {
        background: rgba(255, 255, 255, 0.95);
        border-radius: 20rpx;
        margin-bottom: 20rpx;
        overflow: hidden;
        box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;

        &:active {
            transform: scale(0.98);
            box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.15);
        }

        .record-card {
            padding: 30rpx;
            display: flex;
            align-items: center;
            justify-content: space-between;

            .prize-info {
                display: flex;
                align-items: center;
                flex: 1;

                .prize-icon {
                    width: 80rpx;
                    height: 80rpx;
                    margin-right: 20rpx;
                    border-radius: 15rpx;
                    overflow: hidden;
                    background: #f5f5f5;
                    display: flex;
                    align-items: center;
                    justify-content: center;

                    .icon-img {
                        width: 60rpx;
                        height: 60rpx;
                    }
                }

                .prize-details {
                    flex: 1;

                    .prize-name {
                        display: block;
                        font-size: 32rpx;
                        font-weight: bold;
                        color: #333;
                        margin-bottom: 8rpx;
                    }

                    .prize-desc {
                        display: block;
                        font-size: 24rpx;
                        color: #666;
                        margin-bottom: 8rpx;
                        line-height: 1.4;
                    }

                    .draw-time {
                        display: block;
                        font-size: 24rpx;
                        color: #999;
                    }
                }
            }

            .status-section {
                display: flex;
                flex-direction: column;
                align-items: flex-end;
                gap: 15rpx;

                .status-badge {
                    padding: 8rpx 16rpx;
                    border-radius: 20rpx;
                    font-size: 24rpx;

                    .status-text {
                        font-weight: 500;
                    }

                    &.won {
                        background: linear-gradient(135deg, #ff6b7a, #ff8e9b);
                        color: white;
                    }

                    &.claimed {
                        background: linear-gradient(135deg, #2ed573, #7bed9f);
                        color: white;
                    }

                    &.unclaimed {
                        background: linear-gradient(135deg, #ffa726, #ffcc02);
                        color: white;
                        animation: pulse 2s infinite;
                    }

                    &.not-won {
                        background: #f1f2f6;
                        color: #666;
                    }
                }


            }
        }

        .activity-info {
            padding: 0 30rpx 20rpx;
            border-top: 1rpx solid #f0f0f0;
            margin-top: 10rpx;
            padding-top: 20rpx;

            .activity-name {
                font-size: 24rpx;
                color: #666;
                font-style: italic;
            }
        }
    }
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(255, 167, 38, 0.7);
    }

    70% {
        box-shadow: 0 0 0 10rpx rgba(255, 167, 38, 0);
    }

    100% {
        box-shadow: 0 0 0 0 rgba(255, 167, 38, 0);
    }
}

/* 刷新提示 */
.refresh-tip {
    text-align: center;
    padding: 20rpx;
    color: rgba(255, 255, 255, 0.8);
    font-size: 26rpx;
}

/* 底部提示 */
.bottom-tip {
    text-align: center;
    padding: 40rpx 20rpx;
    color: rgba(255, 255, 255, 0.6);
    font-size: 24rpx;
}

/* 响应式适配 */
@media screen and (max-width: 750rpx) {
    .record-card {
        .prize-info {
            .prize-icon {
                width: 70rpx;
                height: 70rpx;
                margin-right: 15rpx;

                .icon-img {
                    width: 50rpx;
                    height: 50rpx;
                }
            }

            .prize-details {
                .prize-name {
                    font-size: 30rpx;
                }

                .prize-desc {
                    font-size: 22rpx;
                }

                .draw-time {
                    font-size: 22rpx;
                }
            }
        }

        .status-section {
            .status-badge {
                font-size: 22rpx;
                padding: 6rpx 12rpx;
            }

            .claim-btn {
                font-size: 22rpx;
                padding: 10rpx 20rpx;
            }
        }
    }
}
</style>
